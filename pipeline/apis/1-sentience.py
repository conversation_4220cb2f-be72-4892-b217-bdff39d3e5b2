#!/usr/bin/env python3
"""
Module that contains a method to get home planets of all sentient species
using SWAPI API
"""
import requests


def sentientPlanets():
    """
    Returns the list of names of the home planets of all sentient species

    Returns:
        list: List of planet names that are home to sentient species
    """
    planets = []
    url = "https://swapi-api.hbtn.io/api/species/"

    while url:
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()

            # Process each species in the current page
            for species in data.get('results', []):
                classification = species.get('classification', '').lower()
                designation = species.get('designation', '').lower()

                # Check if species is sentient (in either classification or designation)
                if 'sentient' in classification or 'sentient' in designation:
                    homeworld_url = species.get('homeworld')

                    if homeworld_url:
                        try:
                            # Get planet data from homeworld URL
                            planet_response = requests.get(homeworld_url, timeout=10)
                            planet_response.raise_for_status()
                            planet_data = planet_response.json()
                            planet_name = planet_data.get('name')

                            if planet_name and planet_name not in planets:
                                planets.append(planet_name)

                        except requests.RequestException:
                            # If we can't get planet data, skip this species
                            continue

            # Get next page URL for pagination
            url = data.get('next')

        except requests.RequestException:
            # If there's an error with the request, break the loop
            break

    return planets
